import * as THREE from 'three'
import { Clock, Vector3 } from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls'
import { CSS2DRenderer } from 'three/examples/jsm/renderers/CSS2DRenderer'
import { Environment } from './environment'
import { clearObject } from '../utils'
import { PostProcess } from './postProcess'

export let scene, renderer, camera, axesHelper, stats, orbitControls, environment, coreAnimationId, css2dRenderer, postProcess

export const DEFAULT_CAMERA_POS = [-550, 20, 40]
export const DEFAULT_TARGET = [341.3249816894532, 47.79999856948852, 472.44181823730474]
export const CSS2D_CONTAINER_ID = 'css2dRenderer'
export const canvasSize = { width: window.innerWidth, height: window.innerHeight }
const clock = new Clock()

export class Core {
  constructor() {
    this.init()
  }

  init() {
    this.#initScene()
    this.#initCamera()
    this.#initRenderer()
    this.#initCss2dRenderer()
    this.#initOrbitControls()
    this.#initAnimate()
    // 环境：模型、光照、天空盒等
    environment = new Environment()
    // 后处理
    // postProcess = new PostProcess()
    // const room = new GenerateRoom()
    // 显示坐标轴
    // axesHelper = new THREE.AxesHelper(6000)
    // scene.add(axesHelper)
  }

  #initScene() {
    scene = new THREE.Scene()
  }

  #initCamera() {
    camera = new THREE.PerspectiveCamera(30, window.innerWidth / window.innerHeight, 0.1, 100000)
    camera.position.set(...DEFAULT_CAMERA_POS)
  }

  #initRenderer() {
    renderer = new THREE.WebGLRenderer({
      antialias: true, // 是否开启反锯齿
      alpha: true, // 是否可视设置背景色透明
      logarithmicDepthBuffer: true, // 解决模型重叠不停闪烁
    })

    renderer.outputColorSpace = THREE.SRGBColorSpace
    // renderer.toneMapping = THREE.ACESFilmicToneMapping
    // renderer.toneMappingExposure = 0.5

    renderer.setSize(window.innerWidth, window.innerHeight)
    renderer.setPixelRatio(window.devicePixelRatio)

    // 启用阴影渲染
    // renderer.shadowMap.enabled = true
    // renderer.shadowMap.type = THREE.PCFSoftShadowMap
  }

  #initCss2dRenderer() {
    css2dRenderer = new CSS2DRenderer()
    css2dRenderer.setSize(window.innerWidth, window.innerHeight)
    css2dRenderer.domElement.style.position = 'absolute'
    css2dRenderer.domElement.style.top = '0px'
    css2dRenderer.domElement.id = CSS2D_CONTAINER_ID
    // css2dRenderer.domElement.style.pointerEvents = 'none'
  }

  #initOrbitControls() {
    // orbitControls = new OrbitControls(camera, renderer.domElement)
    orbitControls = new OrbitControls(camera, css2dRenderer.domElement)
    orbitControls.target.set(...DEFAULT_TARGET)

    orbitControls.rotateSpeed = 0.3 // 旋转速度 (默认 1.0)
    orbitControls.panSpeed = 0.3 // 平移速度 (默认 1.0)
    orbitControls.zoomSpeed = 0.3 // 缩放速度 (默认 1.0)

    // orbitControls.maxPolarAngle = Math.PI / 2
    // orbitControls.maxDistance = 1000
    // orbitControls.minDistance = 500
    // 禁止拖动
    // orbitControls.enablePan = false
    // 限制移动范围
    // orbitControls.enableDamping = true
    // orbitControls.minZoom = 0.5
    // orbitControls.maxZoom = 2
  }

  /**
   * 设置 orbitControls 的 target 为视角中心点
   * @param {THREE.Object3D} targetObject - 目标对象，如果提供则计算该对象的中心点
   * @param {THREE.Vector3} customCenter - 自定义中心点，如果提供则直接使用
   */
  setTargetToViewCenter(targetObject = null, customCenter = null) {
    let centerPosition

    if (customCenter) {
      // 使用自定义中心点
      centerPosition = customCenter.clone()
    } else if (targetObject) {
      // 计算目标对象的包围盒中心点
      const box3 = new THREE.Box3()
      box3.expandByObject(targetObject)
      centerPosition = new THREE.Vector3()
      box3.getCenter(centerPosition)
    } else {
      // 计算整个场景的中心点
      const box3 = new THREE.Box3()
      box3.expandByObject(scene)
      centerPosition = new THREE.Vector3()
      box3.getCenter(centerPosition)
    }

    // 设置 orbitControls 的 target 为计算出的中心点
    orbitControls.target.copy(centerPosition)
    orbitControls.update()

    return centerPosition
  }

  /**
   * 平滑过渡到新的视角中心点
   * @param {THREE.Object3D} targetObject - 目标对象
   * @param {THREE.Vector3} customCenter - 自定义中心点
   * @param {number} duration - 动画持续时间（秒）
   */
  animateToViewCenter(targetObject = null, customCenter = null, duration = 1) {
    const centerPosition = this.setTargetToViewCenter(targetObject, customCenter)

    // 使用 GSAP 进行平滑过渡（如果可用）
    if (typeof gsap !== 'undefined') {
      gsap.to(orbitControls.target, {
        x: centerPosition.x,
        y: centerPosition.y,
        z: centerPosition.z,
        duration,
        ease: 'power2.inOut',
        onUpdate: () => {
          orbitControls.update()
        },
      })
    } else {
      // 如果没有 GSAP，直接设置
      orbitControls.target.copy(centerPosition)
      orbitControls.update()
    }

    return centerPosition
  }

  #initAnimate() {
    let lastTime = 0
    const targetFPS = 60
    const interval = 1000 / targetFPS
    let isInteracting = false
    const lastCameraPosition = camera.position.clone()
    const lastTargetPosition = orbitControls.target.clone()

    // 监听交互事件
    orbitControls.addEventListener('start', () => {
      isInteracting = true
    })

    orbitControls.addEventListener('end', () => {
      isInteracting = false
    })

    const animate = (currentTime) => {
      coreAnimationId = requestAnimationFrame(animate)

      // 帧率控制
      if (currentTime - lastTime < interval) {
        return
      }
      lastTime = currentTime

      orbitControls.update()

      // 检查是否需要重新渲染
      const cameraChanged = !lastCameraPosition.equals(camera.position)
      const targetChanged = !lastTargetPosition.equals(orbitControls.target)
      const needsRender = isInteracting || cameraChanged || targetChanged || currentTime % (interval * 4) === 0 // 降低静态时的渲染频率

      if (needsRender) {
        renderer.render(scene, camera)
        css2dRenderer.render(scene, camera)

        // 更新上次位置记录
        lastCameraPosition.copy(camera.position)
        lastTargetPosition.copy(orbitControls.target)
      }

      // postProcess.composer.render()
      // 打印相机位置
      // console.log('相机位置', camera.position)
      // console.log('target', orbitControls.target)
    }

    coreAnimationId = requestAnimationFrame(animate)
  }

  changeSize(width, height) {
    canvasSize.width = width
    canvasSize.height = height
    camera.aspect = width / height
    camera.updateProjectionMatrix()
    renderer.setSize(width, height)
    css2dRenderer.setSize(width, height)
    renderer.setPixelRatio(window.devicePixelRatio)
  }

  dispose() {
    cancelAnimationFrame(coreAnimationId)
    environment.dispose()
    clearObject(scene)
    renderer.forceContextLoss()
    renderer.dispose()
    scene.clear()
  }
}

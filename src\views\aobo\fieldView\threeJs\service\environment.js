import * as THREE from 'three'
import { Color, LoadingManager, } from 'three'
import { DRACOLoader } from 'three/examples/jsm/loaders/DRACOLoader'
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader'
import { RGBELoader } from 'three/examples/jsm/loaders/RGBELoader'
import store from '@/store'
import { camera, orbitControls, scene } from '@/views/aobo/fieldView/threeJs/service/core'
import linePoints from '@/assets/linePoints.json'
import linePoints2 from '@/assets/center-line/path_points'
import gsap from 'gsap'

const gui = null

export class Environment {
  loadingManager

  rgbeLoader

  // GLTF模型对象
  model

  singleCabinetModel

  // 加载进度
  progress = 0

  // 模型尺寸
  modelSize

  // 存储一些常用资源
  assets = {
    mainBuilding: null,
  }

  constructor() {
    this.rgbeLoader = new RGBELoader(this.loadingManager)
    this.#initLoadingManager()
    this.#addLight()
    this.#loadModel()
    this.#loadCenterLine()
    this.#loadCenterLine2()

    // scene.add(new THREE.AxesHelper(100000))
  }

  /** 初始化加载管理器*/
  #initLoadingManager() {
    const loadingManager = new LoadingManager()
    this.loadingManager = loadingManager
    loadingManager.onLoad = function() {
      setTimeout(() => {
        store.dispatch('threeJs/setLoaded', true)
      }, 500)
    }

    loadingManager.onProgress = (item, loaded, total) => {
      this.progress = (loaded / total) * 100
      store.dispatch('threeJs/setProgress', this.progress)
    }

    loadingManager.onError = function(url) {
      console.log(`Error loading ${url}`)
    }
  }

  /** 加载环境纹理*/
  #loadEnvironmentTexture() {
    const hdrTexture = this.rgbeLoader.load('model/env.hdr', (texture) => {
      texture.colorSpace = THREE.SRGBColorSpace
      texture.mapping = THREE.EquirectangularReflectionMapping
      scene.environment = texture
      scene.background = texture
    })
  }

  /** 加载模型*/
  #loadModel() {
    const dracoLoader = new DRACOLoader()
    dracoLoader.setDecoderPath('draco/')
    const loader = new GLTFLoader(this.loadingManager)
    loader.setDRACOLoader(dracoLoader)
    loader.load('model/temp.glb', (gltf) => {
      const modelScene = gltf.scene
      scene.add(modelScene)

      this.singleCabinetModel = modelScene

      const changeMaterial = (mesh) => {
        mesh.castShadow = false
        mesh.receiveShadow = false
        // 销毁之前的材质，节约性能
        mesh.material.dispose()
        mesh.material = null
        mesh.material = new THREE.MeshLambertMaterial({
          transparent: true,
          // color: new THREE.Color(color),
          color: new THREE.Color('#4C6B9A'),
          // color: isDoor ? new THREE.Color('#ff6666') : new THREE.Color('#4C6B9A'),
          opacity: 0.7,
          side: THREE.DoubleSide,
        })
      }

      // 不需要更换材质的模型
      const noChangeMaterialList = ['电线']
      // 需要更换材质的模型
      const nameList = ['管廊', '地形', '房屋', '线箱', '支架', '吊装']
      modelScene.traverse((child) => {
        if (child.name.includes('Centerline')) {
          console.log(child, '11111111111')
        } else {
          // child.visible = false
        }
        if (child instanceof THREE.Mesh) {
          // 将材质金属度降低，以便其能接受环境光
          child.material.metalness = 0.3
          child.material.specularIntensity = 1
        }
      })

      modelScene.children.forEach((child) => {
        for (const name of noChangeMaterialList) {
          if (child.name.includes(name)) {
            return
          }
        }

        let flag
        for (const name of nameList) {
          if (child.name.includes(name)) {
            flag = true
            break
          }
        }

        if (flag) {
          if (child instanceof THREE.Group) {
            for (const el of child.children) {
              if (el instanceof THREE.Mesh) {
                changeMaterial(el)
              }
            }
          } else if (child instanceof THREE.Mesh) {
            changeMaterial(child)
          }
        }
      })
      // modelScene.updateMatrixWorld(true)
      dracoLoader.dispose()

      // 将视角定位到光缆起点
      const position = new THREE.Vector3(-538, -26, -6)
      orbitControls.target.copy(position)
      orbitControls.update()
      gsap.to(camera.position, {
        x: -611,
        y: -13,
        z: -42,
        duration: 1,
        ease: 'power2.inOut',
      })
    })
  }

  /** 加载中轴线*/
  #loadCenterLine() {
    // 清空现有的points数组
    const points = []

    // 添加真实数据
    for (const point of linePoints) {
      points.push(new THREE.Vector3(point.x, point.y, point.z))
    }

    // 创建曲线
    const curve = new THREE.CatmullRomCurve3(points)
    // 优化：减少曲线点数量以提升性能
    const finalPoints = curve.getPoints(500) // 从1000减少到500

    // 创建管道中轴线
    const geometry = new THREE.BufferGeometry().setFromPoints(finalPoints)
    const material = new THREE.LineBasicMaterial({ color: 0x00ffff }) // 青色
    const line = new THREE.Line(geometry, material)
    line.name = 'pipeline_main'
    line.rotation.x = -Math.PI / 2
    line.visible = false
    scene.add(line)
  }

  #loadCenterLine2() {
    // 清空现有的points数组
    const points = []

    // 添加真实数据
    for (const point of linePoints2) {
      points.push(new THREE.Vector3(point.x, point.y, point.z))
    }

    // 创建曲线
    const curve = new THREE.CatmullRomCurve3(points)
    // 优化：减少曲线点数量以提升性能
    const finalPoints = curve.getPoints(500) // 从1000减少到500

    // 创建管道中轴线
    const geometry = new THREE.BufferGeometry().setFromPoints(points)
    const material = new THREE.LineBasicMaterial({ color: 0x00ffff }) // 青色
    const line = new THREE.Line(geometry, material)
    line.name = 'pipeline_main'
    // line.rotation.x = -Math.PI / 2
    // line.visible = false
    scene.add(line)
  }

  // 存储光源引用以便清理
  lights = []

  /** 添加光源 - 优化版本，减少光源数量提升性能*/
  #addLight() {
    // 环境光 - 提供基础照明
    const ambientLight = new THREE.AmbientLight(0xffffff, 5)
    scene.add(ambientLight)
    this.lights.push(ambientLight)
  }

  dispose() {
    // 清理光源
    this.lights = []

    // 清理加载器
    if (this.rgbeLoader) {
      this.rgbeLoader = null
    }

    // 清理加载管理器
    this.loadingManager = null
  }
}
